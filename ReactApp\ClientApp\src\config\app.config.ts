export type AppConfig = {
  env: string;
  logLevel: string;
  keycloakEnabled: boolean;
  applicationConfig: {
    appTimeZone: string;
    paginationConfig: {
      pageSizeOptions: number[];
      defaultSize: number;
    };
  };
  roles: Record<string, string>;

  featureFlags: {
    api: {
      GET_LATEST_TERMS: boolean;
      DOWNLOAD_TERMS_PDF: boolean;
      SAVE_TERMS: boolean;
      UPDATE_TERMS: boolean;
      GET_TEMPLATE_BY_ID: boolean;
      CREATE_TEMPLATE: boolean;
      UPDATE_TEMPLATE: boolean;
    };
    ui: {
      CREATE_TEMPLATE: boolean;
      UPDATE_TEMPLATE: boolean;
    };
  };

  temporary: {
    db: string;
    authorization: string;
  };
};

const defaultAppConfig: AppConfig = {
  env: import.meta.env.MODE || "development",
  logLevel: import.meta.env.VITE_LOG_LEVEL || "info",
  keycloakEnabled: import.meta.env.VITE_KEYCLOAK_ENABLED === "true",
  applicationConfig: {
    appTimeZone: "UTC",
    paginationConfig: {
      pageSizeOptions: [10, 20, 50],
      defaultSize: 10,
    },
  },
  roles: {
    system_admin: "System Admin",
    admin: "Admin",
    manager: "Manager",
    officer: "Officer",
  },
  featureFlags: {
    api: {
      GET_LATEST_TERMS: false,
      DOWNLOAD_TERMS_PDF: false,
      SAVE_TERMS: false,
      UPDATE_TERMS: false,
      GET_TEMPLATE_BY_ID: true,
      CREATE_TEMPLATE: false,
      UPDATE_TEMPLATE: false,
    },
    ui: {
      CREATE_TEMPLATE: true,
      UPDATE_TEMPLATE: true,
    },
  },
  temporary: {
    db: "NTB1",
    authorization:
      "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  },
};

export default defaultAppConfig;
