import { useTranslation } from "react-i18next";
import "./Templates.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { Button } from "@progress/kendo-react-buttons";
import { useTemplateGridController } from "./hooks/useTemplateGridController";
import TemplatesGridActions from "./components/TemplatesGridActions";
import TemplateDeleteDialog from "./components/TemplateDeleteDialog";
import { useState } from "react";
import AdvancedBaseGrid from "@/components/common/AdvancedBaseGrid/AdvancedBaseGrid";
import { ManageTemplatePopup } from "./components";
import { useManageTemplatePopup } from "./hooks/useManageTemplatePopup";
import "./components/ManageTemplatePopup.scss";
import appConfig from "@/config/app.config";
import type { GridCustomCellProps } from "@progress/kendo-react-grid";

export default function TemplatesPage() {
  const { t } = useTranslation("dashboard");
  const [openDelete, setOpenDelete] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const handleDeleteDialogClose = () => setOpenDelete(false);
  const {
    columns,
    isColumnsLoading,
    templateList,
    isTemplatesLoading,
    filters,
    pagination,
    sorts,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    handleSortChange,
    totalRecordCount,
  } = useTemplateGridController();

  const {
    isOpen,
    isEditMode,
    editingTemplate,
    editingTemplateId,
    formData,
    validation,
    isSubmitting,
    isTemplateLoading,
    openEditPopup,
    updateField,
    handleCreate,
    handleUpdate,
    handleCancel,
    popupKey,
    handleOpenPopup,
  } = useManageTemplatePopup();

  const handleTemplateCreateSuccess = () => {
    setSuccessMessage(t("toast.templateCreated"));
    // TODO: Refresh the grid here
  };

  const handleTemplateCreateError = () => {
    setErrorMessage(t("toast.templateCreateError"));
  };

  const handleTemplateUpdateSuccess = () => {
    setSuccessMessage(t("toast.templateUpdated"));
    // TODO: Refresh the grid here
  };

  const handleTemplateUpdateError = () => {
    setErrorMessage(t("toast.templateUpdateError"));
  };

  return (
    <SectionLayout
      headerActions={
        <Button
          size="small"
          className="header-action-btn"
          icon="add"
          themeColor="base"
          onClick={handleOpenPopup}
        >
          {t("btn.createTemplate")}
        </Button>
      }
      successMessage={successMessage}
      onCloseSuccess={() => setSuccessMessage("")}
      errorMessage={errorMessage}
      onCloseError={() => setErrorMessage("")}
    >
      <AdvancedBaseGrid
        totalRecordCount={totalRecordCount}
        columns={columns}
        dataSource={templateList}
        filters={filters}
        skip={pagination.skip}
        take={pagination.take}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
        onRefresh={handleRefresh}
        isLoading={isTemplatesLoading}
        sorts={sorts}
        isColumnsLoading={isColumnsLoading}
        actionsColumn={{
          label: t("templates.actions.label"),
          renderer: (props: GridCustomCellProps) => (
            <TemplatesGridActions
              {...props}
              onEdit={() => openEditPopup(props.dataItem.id)}
              onDelete={() => setOpenDelete(true)}
            />
          ),
        }}
        //dataCellMapper={templateGridDataCellMapper}
      />
      {appConfig.featureFlags.ui.CREATE_TEMPLATE && (
        <ManageTemplatePopup
          key={popupKey}
          isOpen={isOpen}
          isEditMode={isEditMode}
          editingTemplate={editingTemplate}
          editingTemplateId={editingTemplateId}
          formData={formData}
          validation={validation}
          isSubmitting={isSubmitting}
          isTemplateLoading={isTemplateLoading}
          onFieldChange={updateField}
          onCreate={(payload) => handleCreate(payload, handleTemplateCreateSuccess, handleTemplateCreateError)}
          onUpdate={(payload) => handleUpdate(payload, handleTemplateUpdateSuccess, handleTemplateUpdateError)}
          onCancel={handleCancel}
        />
      )}
      <TemplateDeleteDialog
        open={openDelete}
        onClose={handleDeleteDialogClose}
      />
    </SectionLayout>
  );
}
