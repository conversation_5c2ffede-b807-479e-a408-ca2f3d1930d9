export interface Template {
  id: string;
  templateName: string;
  createdBy: string;
  createdOn: string;
  description: string;
  status: string;
  assignedClients: string[];
}

export interface TemplateListResponse {
  records: Template[];
  pageCount: number;
  pageNumber: number;
  pageSize: number;
  totalRecordCount: number;
}

export interface TemplateNode {
  id: string;
  name: string;
  childNodes: TemplateChildNode[];
}

export interface TemplateChildNode {
  id: string;
  name: string;
}

export interface TemplateDetailResponse {
  name: string;
  description: string;
  portalBinderStatus: number;
  nodes: TemplateNode[];
}

export interface CreateTemplateRequest {
  name: string;
  description: string;
  parentNodes: CreateTemplateParentNode[];
  portalBinderStatus: number;
}

export interface CreateTemplateParentNode {
  name: string;
  childNodes: CreateTemplateChildNode[];
}

export interface CreateTemplateChildNode {
  name: string;
}

export interface CreateTemplateResponse {
  id: string;
  name: string;
  description: string;
  parentNodes: CreateTemplateParentNode[];
  portalBinderStatus: number;
  success: boolean;
}

export interface UpdateTemplateRequest {
  templateId: string;
  name: string;
  description: string;
  parentNodes: UpdateTemplateParentNode[];
  portalBinderStatus: number;
}

export interface UpdateTemplateParentNode {
  id: number | null;
  name: string;
  childNodes: UpdateTemplateChildNode[];
}

export interface UpdateTemplateChildNode {
  id: number | null;
  name: string;
}

export interface UpdateTemplateResponse {
  templateId: string;
  name: string;
  description: string;
  parentNodes: UpdateTemplateParentNode[];
  portalBinderStatus: number;
  success: boolean;
}