import { createApi } from "@reduxjs/toolkit/query/react";
import type {
  FetchBaseQueryError,
  QueryReturnValue,
} from "@reduxjs/toolkit/query";
import { baseQueryWithReauth } from "./interceptorsSlice";
import type { TypeGridColumn } from "@/types/column";
import { mockTemplateColumns, mockTemplatesData, mockTemplateDetails } from "./mocks/templatesMock";
import type { TemplateListResponse, TemplateDetailResponse, CreateTemplateRequest, CreateTemplateResponse, UpdateTemplateRequest, UpdateTemplateResponse } from "@/types/templates";
import {
  filterBy,
  orderBy,
  type CompositeFilterDescriptor,
  type SortDescriptor,
} from "@progress/kendo-data-query";
import config from "@/config";

interface GetTemplateListParams {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

export const templatesApiSlice = createApi({
  reducerPath: "templatesApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getTemplatesGridColumns: builder.query<TypeGridColumn[], void>({
      queryFn: async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
        return { data: mockTemplateColumns };
      },
    }),
    getTemplateList: builder.query<TemplateListResponse, GetTemplateListParams>(
      {
        queryFn: async ({ skip, take, filters, sorts }) => {
          await new Promise((resolve) => setTimeout(resolve, 500));

          let currentData = [...mockTemplatesData.records];

          currentData = filterBy(currentData, filters);
          currentData = orderBy(currentData, sorts || []);

          const totalRecordCount = currentData.length;
          const pageNumber = Math.floor(skip / take) + 1;
          const pageCount = Math.ceil(totalRecordCount / take);
          const records = currentData.slice(skip, skip + take);

          return {
            data: {
              records,
              totalRecordCount,
              pageCount,
              pageNumber,
              pageSize: take,
            },
          };
        },
      },
    ),

    getTemplateById: builder.query<TemplateDetailResponse, string>({
      async queryFn(
        templateId,
        _queryApi,
        _extraOptions,
        baseQuery,
      ): Promise<
        QueryReturnValue<
          TemplateDetailResponse,
          FetchBaseQueryError,
          {} | undefined
        >
      > {
        if (config.featureFlags.api.GET_TEMPLATE_BY_ID) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          const templateData = mockTemplateDetails[templateId];
          if (templateData) {
            return { data: templateData };
          } else {
            return {
              error: {
                status: 404,
                data: { message: "Template not found" },
              } as FetchBaseQueryError,
            };
          }
        }

        // Real API call (commented for now as per requirements)
        const result = await baseQuery({
          url: `api/PortalBinderTemplates/${templateId}`,
          method: "GET",
        });

        return result as QueryReturnValue<
          TemplateDetailResponse,
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    createTemplate: builder.mutation<CreateTemplateResponse, CreateTemplateRequest>({
      async queryFn(payload, _queryApi, _extraOptions, baseQuery) {
        if (config.featureFlags.api.CREATE_TEMPLATE) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          return { data: { ...payload, id: 'mock-id', success: true } };
        }
        const result = await baseQuery({
          url: 'api/PortalBinderTemplates',
          method: 'POST',
          body: payload,
        });
        return result as QueryReturnValue<CreateTemplateResponse, FetchBaseQueryError, {}>;
      },
    }),
    updateTemplate: builder.mutation<UpdateTemplateResponse, UpdateTemplateRequest>({
      async queryFn(payload, _queryApi, _extraOptions, baseQuery) {
        if (config.featureFlags.api.UPDATE_TEMPLATE) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          return { data: { ...payload, success: true } };
        }
        const { templateId, ...body } = payload;
        const result = await baseQuery({
          url: `api/PortalBinderTemplates/${templateId}`,
          method: 'PUT',
          body,
        });
        return result as QueryReturnValue<UpdateTemplateResponse, FetchBaseQueryError, {}>;
      },
    }),
  }),
});

export const {
  useGetTemplatesGridColumnsQuery,
  useGetTemplateListQuery,
  useGetTemplateByIdQuery,
  useCreateTemplateMutation,
  useUpdateTemplateMutation,
} = templatesApiSlice;
